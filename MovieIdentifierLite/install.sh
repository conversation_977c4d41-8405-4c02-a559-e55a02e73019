#!/bin/bash

# MovieIdentifierLite Installation Script
# This script sets up the environment and installs dependencies

set -e  # Exit on any error

echo "🎬 MovieIdentifierLite Installation Script"
echo "=========================================="

# Check Python version
echo "🐍 Checking Python version..."
python_version=$(python3 --version 2>&1 | awk '{print $2}' | cut -d. -f1,2)
required_version="3.8"

if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" != "$required_version" ]; then
    echo "❌ Error: Python 3.8 or higher is required. Found: $python_version"
    exit 1
fi

echo "✅ Python version: $python_version"

# Create virtual environment
echo "📦 Creating virtual environment..."
if [ ! -d "venv" ]; then
    python3 -m venv venv
    echo "✅ Virtual environment created"
else
    echo "ℹ️  Virtual environment already exists"
fi

# Activate virtual environment
echo "🔧 Activating virtual environment..."
source venv/bin/activate

# Upgrade pip
echo "⬆️  Upgrading pip..."
pip install --upgrade pip

# Install dependencies
echo "📚 Installing dependencies..."
pip install -r requirements.txt

# Create necessary directories
echo "📁 Creating directories..."
mkdir -p models
mkdir -p temp
mkdir -p data
mkdir -p results

# Check for .env file
echo "🔑 Checking environment configuration..."
if [ ! -f ".env" ]; then
    echo "⚠️  No .env file found. Creating from template..."
    cp .env.example .env
    echo "📝 Please edit .env file and add your TMDB API key"
    echo "   Get your free API key from: https://www.themoviedb.org/settings/api"
else
    echo "✅ .env file exists"
fi

# Test installation
echo "🧪 Testing installation..."
python -c "
import sys
sys.path.append('src')
try:
    from src.video_processing import VideoProcessor
    from src.audio_processing import AudioProcessor
    from src.visual_analysis import VisualAnalyzer
    from src.tmdb_api import TMDBClient
    print('✅ All modules imported successfully')
except ImportError as e:
    print(f'❌ Import error: {e}')
    sys.exit(1)
"

echo ""
echo "🎉 Installation completed successfully!"
echo ""
echo "📋 Next steps:"
echo "1. Edit .env file and add your TMDB API key"
echo "2. Activate the virtual environment: source venv/bin/activate"
echo "3. Run the application:"
echo "   • Web UI: python main.py --mode ui"
echo "   • CLI: python main.py --mode cli --video your_video.mp4"
echo ""
echo "📖 For more information, see README.md"
echo ""
