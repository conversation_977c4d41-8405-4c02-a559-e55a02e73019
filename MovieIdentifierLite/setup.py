"""
Setup script for MovieIdentifierLite.
"""

from setuptools import setup, find_packages
from pathlib import Path

# Read README file
readme_file = Path(__file__).parent / "README.md"
long_description = readme_file.read_text(encoding="utf-8") if readme_file.exists() else ""

# Read requirements
requirements_file = Path(__file__).parent / "requirements.txt"
requirements = []
if requirements_file.exists():
    requirements = requirements_file.read_text().strip().split('\n')
    requirements = [req.strip() for req in requirements if req.strip() and not req.startswith('#')]

setup(
    name="movieidentifierlite",
    version="1.0.0",
    author="MovieIdentifierLite Team",
    author_email="<EMAIL>",
    description="A lightweight AI-powered movie identification system for short video clips",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/yourusername/movieidentifierlite",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "Intended Audience :: End Users/Desktop",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Multimedia :: Video",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Software Development :: Libraries :: Python Modules",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=7.4.3",
            "pytest-cov>=4.1.0",
            "black>=23.0.0",
            "flake8>=6.0.0",
            "mypy>=1.0.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "movieidentifierlite=main:main",
        ],
    },
    include_package_data=True,
    package_data={
        "": ["config/*.yaml", "examples/*.py", "examples/*.md"],
    },
    keywords="movie identification ai video analysis whisper blip clip tmdb",
    project_urls={
        "Bug Reports": "https://github.com/yourusername/movieidentifierlite/issues",
        "Source": "https://github.com/yourusername/movieidentifierlite",
        "Documentation": "https://github.com/yourusername/movieidentifierlite/blob/main/README.md",
    },
)
