#!/usr/bin/env python3
"""
MovieIdentifierLite - Main Application Entry Point
A lightweight AI-powered movie identification system for short video clips.
"""

import os
import sys
import argparse
from pathlib import Path

# Add src directory to Python path
sys.path.append(str(Path(__file__).parent / "src"))

from src.identification_engine.movie_identifier import MovieIdentifier
from src.ui.streamlit_app import run_streamlit_app


def main():
    """Main application entry point."""
    parser = argparse.ArgumentParser(
        description="MovieIdentifierLite - Identify movies from short video clips"
    )
    parser.add_argument(
        "--mode", 
        choices=["cli", "ui"], 
        default="ui",
        help="Run mode: cli for command line, ui for Streamlit interface"
    )
    parser.add_argument(
        "--video", 
        type=str,
        help="Path to video file (required for CLI mode)"
    )
    parser.add_argument(
        "--config", 
        type=str,
        default="config/config.yaml",
        help="Path to configuration file"
    )
    
    args = parser.parse_args()
    
    if args.mode == "cli":
        if not args.video:
            print("Error: --video argument is required for CLI mode")
            sys.exit(1)
        
        # CLI mode
        identifier = MovieIdentifier(config_path=args.config)
        result = identifier.identify_movie(args.video)
        
        if result:
            print(f"\n🎬 Movie Identified: {result['title']}")
            print(f"📅 Release Year: {result['release_year']}")
            print(f"🎭 Director: {result['director']}")
            print(f"⭐ Cast: {', '.join(result['cast'][:5])}")
            print(f"📊 Confidence: {result['confidence']:.2f}")
        else:
            print("❌ Could not identify the movie from the provided clip.")
    
    else:
        # UI mode
        run_streamlit_app()


if __name__ == "__main__":
    main()
