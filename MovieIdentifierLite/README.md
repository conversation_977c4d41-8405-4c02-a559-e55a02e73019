# 🎬 MovieIdentifierLite

A lightweight AI-powered movie identification system that can analyze short video clips (less than 1 minute) and identify the movie name, director, release year, and cast using free tools and APIs.

## ✨ Features

- **🎥 Video Processing**: Extracts key frames using OpenCV
- **🎵 Audio Transcription**: Uses Whisper (tiny/base) for speech-to-text
- **👁️ Visual Analysis**: Employs BLIP/CLIP models for scene description
- **🔍 Movie Search**: Integrates with TMDB API for movie metadata
- **🖥️ User Interface**: Simple Streamlit web interface
- **💾 Low RAM Optimized**: Works on systems with 6GB RAM or less
- **🚫 No GPU Required**: Runs entirely on CPU

## 🛠️ System Requirements

- **RAM**: 6GB minimum (optimized for low-memory systems)
- **CPU**: Any modern processor (no GPU required)
- **Python**: 3.8 or higher
- **Internet**: Required for TMDB API access and model downloads
- **Storage**: ~2GB for AI models (downloaded automatically on first run)

## 📦 Installation

### 1. Clone the Repository

```bash
git clone <repository-url>
cd MovieIdentifierLite
```

### 2. Create Virtual Environment

```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

### 3. Install Dependencies

```bash
pip install -r requirements.txt
```

### 4. Setup TMDB API Key

1. Get a free API key from [The Movie Database (TMDB)](https://www.themoviedb.org/settings/api)
2. Copy the example environment file:
   ```bash
   cp .env.example .env
   ```
3. Edit `.env` and add your API key:
   ```
   TMDB_API_KEY=your_actual_api_key_here
   ```

## 🚀 Usage

### Web Interface (Recommended)

Launch the Streamlit web interface:

```bash
python main.py --mode ui
```

Then open your browser to `http://localhost:8501`

### Command Line Interface

Identify a movie from a video file:

```bash
python main.py --mode cli --video path/to/your/video.mp4
```

## 📁 Project Structure

```
MovieIdentifierLite/
├── main.py                     # Main application entry point
├── requirements.txt            # Python dependencies
├── README.md                   # This file
├── .env.example               # Environment variables template
├── config/
│   └── config.yaml            # Configuration settings
├── src/
│   ├── video_processing/      # Video frame extraction
│   ├── audio_processing/      # Audio transcription with Whisper
│   ├── visual_analysis/       # BLIP/CLIP visual analysis
│   ├── tmdb_api/             # TMDB API integration
│   ├── identification_engine/ # Core movie identification logic
│   └── ui/                   # Streamlit web interface
├── models/                   # Downloaded AI models (auto-created)
├── data/                     # Sample data and examples
├── tests/                    # Unit tests
└── examples/                 # Example scripts and demos
```

## ⚙️ Configuration

Edit `config/config.yaml` to customize:

- **Video Processing**: Frame extraction settings, supported formats
- **Audio Processing**: Whisper model size (tiny/base), language settings
- **Visual Analysis**: BLIP/CLIP model selection, batch sizes
- **Movie Identification**: Confidence thresholds, scoring weights
- **System Settings**: RAM limits, temporary directories

## 🎯 How It Works

1. **Video Validation**: Checks file format and duration (max 60 seconds)
2. **Frame Extraction**: Extracts key frames at specified intervals
3. **Audio Processing**: Extracts audio and transcribes using Whisper
4. **Visual Analysis**: Generates descriptions of video frames using BLIP
5. **Movie Search**: Searches TMDB database using transcribed text and visual cues
6. **Smart Matching**: Combines audio, visual, and metadata scores to identify the movie
7. **Result Ranking**: Returns the best match with confidence score

## 📊 Supported Formats

**Video Formats**: MP4, AVI, MOV, MKV, WebM
**Maximum Duration**: 60 seconds (configurable)
**Recommended**: Clear audio, distinctive scenes, good lighting

## 🔧 Troubleshooting

### Common Issues

**"TMDB API key not provided"**
- Ensure your `.env` file contains a valid TMDB API key
- Check that the environment variable is properly loaded

**"Out of memory" errors**
- Reduce batch size in `config/config.yaml`
- Use Whisper "tiny" model instead of "base"
- Close other applications to free RAM

**"No movie identified"**
- Try clips with clearer dialogue
- Use scenes with distinctive visual elements
- Lower the confidence threshold
- Ensure the movie exists in TMDB database

**Slow processing**
- First run downloads models (~2GB) - this is normal
- Subsequent runs should be much faster
- Consider using shorter video clips

### Performance Tips

- **First Run**: Allow extra time for model downloads
- **RAM Usage**: Monitor system memory, close unnecessary applications
- **Video Quality**: Better quality videos yield better results
- **Clip Selection**: Choose scenes with dialogue and distinctive visuals

## 🧪 Testing

Run the test suite:

```bash
pytest tests/
```

Run with coverage:

```bash
pytest tests/ --cov=src --cov-report=html
```

## 📝 Examples

### Basic Usage

```python
from src.identification_engine import MovieIdentifier

# Initialize identifier
identifier = MovieIdentifier("config/config.yaml")

# Identify movie from video
result = identifier.identify_movie("sample_video.mp4")

if result:
    print(f"Movie: {result['title']}")
    print(f"Year: {result['release_year']}")
    print(f"Director: {result['director']}")
    print(f"Confidence: {result['confidence']:.2f}")
```

### Custom Configuration

```python
# Load custom configuration
config = {
    'video': {'max_duration': 30},
    'audio': {'whisper_model': 'tiny'},
    'identification': {'confidence_threshold': 0.5}
}

identifier = MovieIdentifier()
identifier.config.update(config)
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [OpenAI Whisper](https://github.com/openai/whisper) for audio transcription
- [Hugging Face](https://huggingface.co/) for BLIP and CLIP models
- [The Movie Database (TMDB)](https://www.themoviedb.org/) for movie metadata
- [Streamlit](https://streamlit.io/) for the web interface
- [OpenCV](https://opencv.org/) for video processing

## 📞 Support

If you encounter any issues or have questions:

1. Check the [Troubleshooting](#-troubleshooting) section
2. Search existing [Issues](https://github.com/your-repo/issues)
3. Create a new issue with detailed information about your problem

---

**Made with ❤️ for movie enthusiasts and AI developers**
